"""
Assessment-related API routes for the quiz/assessment management system.
"""

import os
import random
from datetime import datetime
from typing import List, Optional

import psycopg2
import psycopg2.extras
from fastapi import APIRouter, Depends, HTTPException, Query, status
from pydantic import BaseModel

from ...api.middlewares.hashid_middleware import hash_ids_in_response
from ...config.db_config import DATABASE_CONFIG
from ...models.assessment_manager import (
    add_questions_to_fixed_assessment,
    assessment_report_by_topic,
    assessment_report_by_user,
    assessment_report_with_question_stats,
    create_assessment_with_skills,
    get_assessment_by_id,
    get_assessment_questions_by_id,
    get_assessments_paginated,
    get_assessments_with_sessions,
    get_fixed_assessment_questions,
    get_single_assessment_by_id,
    get_user_assessments_by_email,
    get_user_assessments_by_id,
    insert_quiz_creation_logs,
    validate_skills_exist,
)
from ...models.db_manager import (
    count_questions_for_skills,
)
from ...utils.api_response import (
    error_response,
    raise_http_exception,
    success_response,
)
from ...utils.hashid_utils import (
    decode_assessment_id,
    encode_session_code,
)
from ...utils.logger import (
    error,
    info,
)


# Create a simple rate limiter dependency
async def rate_limiter():
    """Simple rate limiter placeholder"""
    return None


# Create router for assessment-related endpoints
assessment_router = APIRouter()


# Pydantic models for assessment-related requests
class CreateQuizRequest(BaseModel):
    topic: str  # This is the user-defined description for the assessment
    quiz_name: str
    user_id: str
    skill_ids: List[int]  # Now properly used for many-to-many
    question_selection_mode: str = "dynamic"  # 'fixed' or 'dynamic'
    duration: int = 30  # Duration in minutes


class FinalQuestionsRequest(BaseModel):
    question_ids: List[int]
    quiz_name: str
    assessment_id: Optional[int] = None  # For fixed assessments


class ReportRequest(BaseModel):
    report_type: str
    user_name: Optional[str] = None
    # This refers to assessment name or part of it
    report_topic: Optional[str] = None
    # For assessment-wise reports
    assessment_base_name: Optional[str] = None
    quiz_type: Optional[str] = None


def _validate_quiz_request(request: CreateQuizRequest):
    """Validate the quiz creation request parameters."""
    assessment_description = request.topic
    user_defined_assessment_name = request.quiz_name

    if not assessment_description or len(assessment_description) < 20:
        raise_http_exception(
            status_code=400,
            detail="Assessment description must be at least 20 characters long",
        )

    if not user_defined_assessment_name or len(user_defined_assessment_name) < 3:
        raise_http_exception(
            status_code=400,
            detail="Assessment name must be at least 3 characters long",
        )


def _validate_skills(skill_ids: List[int]):
    """Validate that all skill IDs exist in the database."""
    if not skill_ids:
        raise_http_exception(
            status_code=400,
            detail="At least one skill ID must be provided",
        )

    all_exist, missing_skills = validate_skills_exist(skill_ids)
    if not all_exist:
        raise_http_exception(
            status_code=400,
            detail=f"Skills with IDs {missing_skills} do not exist",
        )


def _check_question_availability(skill_ids: List[int], question_selection_mode: str) -> int:
    """Check if there are enough questions available for the selected skills."""
    # question_selection_mode is kept for future use but not currently used in validation
    _ = question_selection_mode  # Suppress unused parameter warning
    question_count = count_questions_for_skills(skill_ids)

    if question_count == 0:
        raise_http_exception(
            status_code=400,
            detail="No questions found for the selected skills. Please add questions first.",
        )

    return question_count


def _create_assessment_in_db(
    assessment_name: str, assessment_description: str, question_selection_mode: str, duration: int, skill_ids: list[int]
) -> int:
    """Create assessment in database and return assessment ID."""
    assessment_id = create_assessment_with_skills(
        assessment_name, assessment_description, question_selection_mode, duration, skill_ids
    )
    if assessment_id is None:
        raise Exception("Failed to create assessment in database")
    return assessment_id


@assessment_router.post("/admin/quiz")
async def create_quiz(request: CreateQuizRequest, _: None = Depends(rate_limiter)):
    """
    Creates a new assessment based on the provided description and quiz name.

    Args:
        request (CreateQuizRequest): The request body containing:
            topic (str): The user-defined description for the assessment.
            quiz_name (str): The user-defined name for this assessment.
            user_id (str): The ID of the admin creating the quiz.
            skill_ids (list[int]): List of skill IDs to associate with this assessment.

    Returns:
        JSONResponse: A JSON response containing the ID of the created assessment,
        and a success message.
        Admins should then use the /generate_sessions endpoint to create usable session codes.

    Raises:
        HTTPException:
            - 400: If the description is missing or no questions are found.
            - 500: If an error occurs during quiz creation.
    """
    try:
        # Validate request parameters
        _validate_quiz_request(request)

        # Extract request data
        assessment_description = request.topic
        user_defined_assessment_name = request.quiz_name
        user_id = request.user_id
        skill_ids = request.skill_ids
        question_selection_mode = request.question_selection_mode
        duration = request.duration

        # Validate skills
        _validate_skills(skill_ids)

        # Check question availability
        question_count = _check_question_availability(skill_ids, question_selection_mode)
        info(f"Found {question_count} existing questions for the selected skills")

        # Create assessment name with timestamp
        timestamp = datetime.now().strftime("%d_%m_%Y")
        assessment_base_name = f"{user_defined_assessment_name}_{timestamp}"
        assessment_name = f"{assessment_base_name} Assessment"

        # Create assessment in database
        assessment_id = _create_assessment_in_db(
            assessment_name, assessment_description, question_selection_mode, duration, skill_ids
        )

        # Log the assessment creation
        insert_quiz_creation_logs(
            [
                {
                    "user_id": user_id,
                    "assessment_name": assessment_name,
                    "assessment_description": assessment_description,
                    "total_questions": question_count,
                    "assessment_id": assessment_id,
                }
            ]
        )

        return success_response(
            data={
                "assessment_id": assessment_id,
                "assessment_base_name": assessment_base_name,
                "question_selection_mode": question_selection_mode,
                "skill_ids": skill_ids,
                "total_questions_available": question_count,
                "duration": duration,
            },
            message="Assessment created successfully. Please generate sessions to get usable codes.",
        )

    except Exception as e:
        error(f"Error creating quiz: {str(e)}", exc_info=True)
        raise_http_exception(status_code=500, detail=f"Error creating assessment: {str(e)}")


@assessment_router.get("/admin/assessments")
async def get_assessments(
    limit: int = Query(7, ge=1, le=100),
    offset: int = Query(0, ge=0),
):
    """
    Get all assessments with pagination

    Args:
        limit: Maximum number of items per page (default: 3)
        offset: Starting position (default: 0)
    """
    try:
        assessments, total = get_assessments_paginated(limit, offset)

        # Apply hash ID transformation
        hashed_assessments = hash_ids_in_response(assessments)

        return success_response(
            data={
                "assessments": hashed_assessments,
                "pagination": {
                    "total": total,
                    "limit": limit,
                    "offset": offset,
                    "has_more": offset + limit < total,
                },
            },
            message="Assessments retrieved successfully",
        )

    except Exception as e:
        error(f"Error fetching assessments: {str(e)}")
        return error_response(
            message=f"Error fetching assessments: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@assessment_router.get("/admin/assessment/{assessment_id}")
async def get_assessment(
    assessment_id: str,
    include_questions: bool = Query(False),
    include_answers: bool = Query(False),
):
    """
    Get a single assessment without questions by default.
    For security, questions and answers are excluded unless explicitly requested.
    """
    try:
        # Try to decode if it's a hash, otherwise treat as integer
        actual_id = assessment_id
        if not assessment_id.isdigit():
            decoded_id = decode_assessment_id(assessment_id)
            if decoded_id is not None:
                actual_id = decoded_id
            else:
                return error_response(
                    message=f"Invalid assessment ID format: {assessment_id}",
                    code=status.HTTP_400_BAD_REQUEST,
                    error_type="BadRequest",
                )
        else:
            actual_id = int(assessment_id)

        # For security, always exclude questions by default
        # Parameters are kept for API compatibility but not used for security
        safe_include_questions = False  # Always False for security
        safe_include_answers = False  # Always False for security

        # Suppress unused parameter warnings
        _ = include_questions
        _ = include_answers

        # Use the new function from assessment_manager
        assessment_dict = get_assessment_by_id(
            actual_id,
            include_questions=safe_include_questions,
            include_answers=safe_include_answers,
        )

        if not assessment_dict:
            return error_response(
                message=f"Assessment with ID {actual_id} not found",
                code=status.HTTP_404_NOT_FOUND,
                error_type="NotFound",
            )

        # Transform response to include hashed IDs
        hashed_assessment = hash_ids_in_response(assessment_dict)

        return success_response(
            data=hashed_assessment,
            message="Assessment retrieved successfully",
        )

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error fetching assessment {assessment_id}: {str(e)}")
        return error_response(
            message=f"Error fetching assessment: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@assessment_router.get("/admin/assessment-questions/{assessment_id}")
async def get_assessment_questions(assessment_id: int):
    """Get all available questions for an assessment based on its associated skills"""
    try:
        # Use the new function from assessment_manager
        result = get_assessment_questions_by_id(assessment_id)

        if result is None:
            return error_response(
                message=f"Assessment with ID {assessment_id} not found",
                code=status.HTTP_404_NOT_FOUND,
                error_type="NotFound",
            )

        return success_response(
            data=result,
            message="Assessment questions retrieved successfully",
        )

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error fetching questions for assessment {assessment_id}: {str(e)}")
        return error_response(
            message=f"Error fetching questions for assessment: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@assessment_router.get("/admin/fixed-assessment-questions/{assessment_id}")
async def get_fixed_assessment_questions_endpoint(assessment_id: int):
    """Get the assigned questions for a fixed assessment"""
    try:
        result = get_fixed_assessment_questions(assessment_id)

        if result is None:
            return error_response(
                message=f"Assessment with ID {assessment_id} not found",
                code=status.HTTP_404_NOT_FOUND,
                error_type="NotFound",
            )

        if "error" in result:
            if result["error"] == "not_fixed":
                return error_response(
                    message=result["message"],
                    code=status.HTTP_400_BAD_REQUEST,
                    error_type="BadRequest",
                )

        return success_response(
            data=result,
            message=f"Retrieved {len(result['question_ids'])} fixed questions for assessment '{result['assessment_name']}'",
        )

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error fetching fixed assessment questions for assessment {assessment_id}: {str(e)}")
        return error_response(
            message=f"Error fetching fixed assessment questions: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@assessment_router.get("/admin/assessments/{assessment_id}")
async def get_single_assessment(assessment_id: int):
    """Get a single assessment by ID for the quiz link page"""
    try:
        assessment = get_single_assessment_by_id(assessment_id)

        if not assessment:
            raise_http_exception(status_code=404, detail=f"Assessment with ID {assessment_id} not found")

        return assessment

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error fetching single assessment {assessment_id}: {str(e)}")
        return error_response(
            message=f"Error fetching assessment: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@assessment_router.get("/admin/assessments-with-sessions")
async def get_assessments_with_sessions_endpoint():
    """Get only assessments that have existing sessions"""
    try:
        assessments = get_assessments_with_sessions()

        # Apply hash ID transformation
        hashed_assessments = hash_ids_in_response(assessments)

        return success_response(
            data={"assessments": hashed_assessments},
            message="Assessments with sessions retrieved successfully",
        )

    except Exception as e:
        error(f"Error fetching assessments with sessions: {str(e)}")
        return error_response(
            message=f"Error fetching assessments with sessions: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@assessment_router.post("/admin/reports")
async def generate_report(request: ReportRequest, _: None = Depends(rate_limiter)):
    """Generate reports based on user or topic"""
    try:
        if request.report_type == "user_wise" and request.user_name:
            base_report, score_report = assessment_report_by_user(request.user_name, request.quiz_type or "mock")
            return success_response(
                data={
                    "base_report": base_report,
                    "score_report": score_report,
                },
                message=f"Generated user-wise report for {request.user_name}",
            )

        elif request.report_type == "topic_wise" and request.report_topic:
            base_report, score_report = assessment_report_by_topic(request.report_topic, request.quiz_type or "mock")
            return success_response(
                data={
                    "base_report": base_report,
                    "score_report": score_report,
                },
                message=f"Generated topic-wise report for {request.report_topic}",
            )

        elif request.report_type == "assessment_wise" and request.assessment_base_name:
            base_report, score_report = assessment_report_with_question_stats(
                request.assessment_base_name, request.quiz_type or "mock"
            )
            return success_response(
                data={
                    "base_report": base_report,
                    "score_report": score_report,
                },
                message=f"Generated assessment-wise report for {request.assessment_base_name}",
            )

        else:
            return error_response(
                message="Invalid report type or missing required parameters",
                code=status.HTTP_400_BAD_REQUEST,
                error_type="BadRequest",
            )

    except Exception as e:
        error(f"Error generating report: {str(e)}")
        return error_response(
            message=f"Error generating report: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@assessment_router.get("/admin/users/{user_id}/assessments")
async def get_user_assessments(user_id: int):
    """Get all assessments taken by a specific user with their scores"""
    try:
        result = get_user_assessments_by_id(user_id)

        if result is None:
            return error_response(
                message=f"User with ID {user_id} not found",
                code=status.HTTP_404_NOT_FOUND,
                error_type="NotFound",
            )

        hashed_data = hash_ids_in_response(result)
        return success_response(
            data=hashed_data,
            message="User assessments retrieved successfully",
        )
    except HTTPException:
        raise
    except Exception as e:
        error(f"Error getting user assessments: {str(e)}")
        return error_response(
            message=f"Error getting user assessments: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@assessment_router.get("/user/{email}/assessments")
async def get_user_assessments_by_email_endpoint(email: str):
    """Get all assessments taken by a specific user identified by email"""
    try:
        result = get_user_assessments_by_email(email)

        if result is None:
            return error_response(
                message=f"User with email {email} not found",
                code=status.HTTP_404_NOT_FOUND,
                error_type="NotFound",
            )

        hashed_data = hash_ids_in_response(result)
        return success_response(
            data=hashed_data,
            message=f"User assessments retrieved successfully for {email}",
        )
    except HTTPException:
        raise
    except Exception as e:
        error(f"Error getting user assessments by email: {str(e)}")
        return error_response(
            message=f"Error getting user assessments by email: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@assessment_router.post("/admin/final-questions")
async def add_final_questions(request: FinalQuestionsRequest, _: None = Depends(rate_limiter)):
    """Add questions to a final assessment or a fixed assessment"""
    try:
        # Check if this is for a fixed assessment
        if request.assessment_id:
            success, message = add_questions_to_fixed_assessment(request.assessment_id, request.question_ids)

            if not success:
                if "not found" in message:
                    raise_http_exception(status_code=404, detail=message)
                else:
                    raise_http_exception(status_code=400, detail=message)

            return success_response(
                data={
                    "assessment_id": request.assessment_id,
                    "questions_added": len(request.question_ids),
                },
                message=message,
            )

        else:
            # This is for creating a new final assessment (legacy behavior)
            raise_http_exception(
                status_code=400,
                detail="assessment_id is required for adding questions to assessments",
            )

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error adding final questions: {str(e)}")
        return error_response(
            message=f"Error adding final questions: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@assessment_router.post("/admin/generate-link")
async def generate_quiz_link(request_data: dict, _: None = Depends(rate_limiter)):
    """Generate a shareable link for users to take an assessment"""
    try:
        assessment_id = request_data.get("assessment_id")

        if not assessment_id:
            raise_http_exception(status_code=400, detail="Assessment ID is required")

        # Verify the assessment exists
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute("SELECT id, name FROM assessments WHERE id = %s", (assessment_id,))
                assessment = cur.fetchone()

                if not assessment:
                    raise_http_exception(status_code=404, detail=f"Assessment with ID {assessment_id} not found")

        frontend_url = os.getenv("FRONTEND_URL", "http://localhost:5173")

        # Generate a unique 6-digit session code
        session_code = str(random.randint(100000, 999999)).zfill(6)
        hashed_session_code = encode_session_code(session_code)
        quiz_link = f"{frontend_url}/quiz/{hashed_session_code}"

        response_data = {
            "link": quiz_link,
            "assessment_id": assessment_id,
            "assessment_name": assessment["name"],
            "session_code": session_code,
            "hashed_session_code": hashed_session_code,
        }
        hashed_data = hash_ids_in_response(response_data)
        return success_response(data=hashed_data, message="Quiz link generated successfully")

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error generating quiz link: {str(e)}")
        raise_http_exception(status_code=500, detail=f"Error generating quiz link: {str(e)}")
