"""
Skill-related API routes for the quiz/assessment management system.
"""

import os
import re
import time
from string import Template
from typing import Optional, Union

import httpx
import psycopg2
import psycopg2.extras
from fastapi import APIRouter, HTTPException, Query, status
from pydantic import BaseModel, field_validator

from ...api.middlewares.hashid_middleware import hash_ids_in_response
from ...config.db_config import DATABASE_CONFIG
from ...models.skill_manager import (
    create_skill,
    get_question_count_for_skill,
    get_skill_by_id,
    get_skill_id_by_name,
    get_skill_question_counts,
    get_skill_questions_paginated,
    get_skills_paginated,
    get_skillwise_heatmap_data,
    get_user_skill_performance_detailed,
    get_user_skill_performance_detailed_by_email,
)
from ...services.create_quiz_questions import load_yaml_prompt
from ...services.llm_client import query_model
from ...utils.api_response import (
    error_response,
    paginated_response,
    raise_http_exception,
    success_response,
)
from ...utils.hashid_utils import (
    decode_skill_id,
    detect_hash_type,
)
from ...utils.logger import (
    debug,
    error,
    info,
    warning,
)

# Create router for skill-related endpoints
skill_router = APIRouter()


# Pydantic models for skill-related requests
class CreateSkillRequest(BaseModel):
    name: str
    description: str = ""


class SuggestSkillDescriptionRequest(BaseModel):
    skill_name: str
    existing_description: Optional[str] = None


class QuestionGenerationTaskRequest(BaseModel):
    """Request model for question generation task publishing"""

    skill_id: Union[int, str]  # Accept both int and string
    skill_name: str
    skill_description: str

    @field_validator("skill_id", mode="before")
    def validate_skill_id(cls, v):
        # Accept both int and string, return as is for processing in endpoint
        return v


# Helper functions for skill operations
def _validate_skill_generation_request(skill_id, skill_name: str, skill_description: str):
    """Validate skill question generation request parameters."""
    if not skill_id or not skill_name or not skill_description:
        return error_response(
            message="Skill ID, name, and description are required",
            code=status.HTTP_400_BAD_REQUEST,
            error_type="BadRequest",
        )

    if len(skill_description.strip()) < 20:
        return error_response(
            message="Skill description must be at least 20 characters long for effective question generation",
            code=status.HTTP_400_BAD_REQUEST,
            error_type="BadRequest",
        )

    return None


def _process_skill_id(skill_id, skill_name: str):
    """Process and convert skill_id to numeric format."""
    if isinstance(skill_id, str):
        try:
            # Check if it's a hash ID
            decoded_id = decode_skill_id(skill_id)
            if decoded_id is not None:
                skill_id = decoded_id
                info(f"Converted hash ID {skill_id} to numeric ID {decoded_id}")
            else:
                # Try to convert to int directly
                try:
                    skill_id = int(skill_id)
                    info(f"Converted string ID {skill_id} to numeric ID")
                except ValueError:
                    # If we can't convert to int, try to find the skill by name
                    found_skill_id = get_skill_id_by_name(skill_name)
                    if found_skill_id:
                        skill_id = found_skill_id
                        info(f"Found skill ID {skill_id} by name {skill_name}")
                    else:
                        return None, error_response(
                            message=(
                                f"Invalid skill ID format: {skill_id} and could not find skill by name: "
                                f"{skill_name}"
                            ),
                            code=status.HTTP_400_BAD_REQUEST,
                            error_type="BadRequest",
                        )
        except Exception as e:
            error(f"Error processing skill ID: {e}")
            return None, error_response(
                message=f"Invalid skill ID format: {skill_id}. Error: {str(e)}",
                code=status.HTTP_400_BAD_REQUEST,
                error_type="BadRequest",
            )

    return skill_id, None


def _validate_environment_config():
    """Validate environment configuration for question generation."""
    model_id = os.getenv("MODEL_ID")
    if not model_id:
        return error_response(
            message="Question generation service is not properly configured. MODEL_ID is missing.",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )

    base_url = os.getenv("BASE_URL")
    api_key = os.getenv("API_KEY")
    if not base_url or not api_key:
        return error_response(
            message="Question generation service is not properly configured. API credentials are missing.",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )
    return None


def _get_skill_question_count(skill_id: int):
    """Get current question count for a skill."""
    count = get_question_count_for_skill(skill_id)
    if count is None:
        return None, error_response(
            message=f"Skill with ID {skill_id} not found",
            code=status.HTTP_404_NOT_FOUND,
            error_type="NotFound",
        )
    return count, None


async def _publish_to_dapr(task_data: dict) -> bool:
    """Publish task to Dapr Pub/Sub."""
    try:
        dapr_endpoint = os.getenv("DAPR_ENDPOINT", "http://localhost:3500")
        dapr_url = f"{dapr_endpoint}/v1.0/publish/pubsub/generate-questions"

        async with httpx.AsyncClient() as client:
            response = await client.post(
                dapr_url,
                json=task_data,
                headers={"Content-Type": "application/json"},
                timeout=5.0,
            )

            if response.status_code == 204:
                info("Successfully published task to Dapr")
                return True
            else:
                warning(f"Dapr publish failed: {response.status_code} - {response.text}")
                return False

    except (httpx.TimeoutException, httpx.RequestError, Exception) as e:
        warning(f"Dapr not available ({e}), falling back to direct worker call")
        return False


async def _publish_to_worker(task_data: dict):
    """Publish task directly to worker service."""
    try:
        worker_endpoint = os.getenv("WORKER_ENDPOINT", "http://localhost:8001")
        worker_url = f"{worker_endpoint}/question-tasks"

        async with httpx.AsyncClient() as client:
            response = await client.post(
                worker_url,
                json={"data": task_data},
                headers={"Content-Type": "application/json"},
                timeout=10.0,
            )

            if response.status_code != 200:
                error(f"Failed to call worker directly: {response.status_code} - {response.text}")
                raise_http_exception(
                    status_code=500, detail="Failed to start question generation task. Please try again."
                )

            info("Successfully sent task to worker directly")

    except httpx.TimeoutException:
        info("Worker call timed out, but task is likely still processing in background")
    except httpx.RequestError as e:
        error(f"Network error while calling worker: {e}")
        raise_http_exception(
            status_code=500,
            detail="Network error while starting task. Please check if the worker service is running.",
        )
    except Exception as e:
        error(f"Unexpected error while calling worker: {e}")
        raise_http_exception(status_code=500, detail="An unexpected error occurred. Please try again.")


# Skill CRUD endpoints
@skill_router.post("/admin/skills")
async def create_skill_endpoint(request: CreateSkillRequest):
    """Create a new skill"""
    try:
        skill = create_skill(request.name, request.description)
        if skill is None:
            raise_http_exception(status_code=500, detail="Failed to create skill")

        hashed_skill = hash_ids_in_response(skill)
        return success_response(data={"skill": hashed_skill}, message="Skill created successfully")
    except ValueError as e:
        raise_http_exception(status_code=400, detail=str(e))
    except Exception as e:
        error(f"Error creating skill: {str(e)}")
        raise_http_exception(status_code=500, detail=f"Error creating skill: {str(e)}")


@skill_router.get("/admin/skills")
async def get_skills(
    limit: int = Query(7, ge=1, le=100),
    offset: int = Query(0, ge=0),
):
    """
    Get skills with pagination

    Args:
        limit: Maximum number of items per page (default: 7)
        offset: Starting position (default: 0)
    """
    try:
        skills, total = get_skills_paginated(limit, offset)

        # Transform response to include hashed IDs
        hashed_skills = hash_ids_in_response(skills)

        # Return paginated response
        return paginated_response(
            data=hashed_skills,
            total=total,
            limit=limit,
            offset=offset,
            message="Skills retrieved successfully",
        )
    except Exception as e:
        error(f"Error getting skills: {str(e)}")
        return error_response(
            message="Failed to retrieve skills",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@skill_router.get("/admin/skills/{skill_id}")
async def get_skill(skill_id: str):
    """Get a single skill with its details"""
    try:
        # Try to decode if it's a hash, otherwise treat as integer
        actual_id = skill_id
        if not skill_id.isdigit():
            decoded_id = decode_skill_id(skill_id)
            if decoded_id is not None:
                actual_id = decoded_id
            else:
                # Check if it's a hash for a different entity type
                detected_type = detect_hash_type(skill_id)
                if detected_type:
                    return error_response(
                        message=f"Invalid hash type: '{skill_id}' is a {detected_type} hash, not a skill hash",
                        code=status.HTTP_400_BAD_REQUEST,
                        error_type="BadRequest",
                    )
                else:
                    return error_response(
                        message=f"Invalid skill ID format: {skill_id}",
                        code=status.HTTP_400_BAD_REQUEST,
                        error_type="BadRequest",
                    )
        else:
            actual_id = int(skill_id)

        skill_dict = get_skill_by_id(actual_id)

        if not skill_dict:
            return error_response(
                message=f"Skill with ID {actual_id} not found",
                code=status.HTTP_404_NOT_FOUND,
                error_type="NotFound",
            )

        # Transform response to include hashed IDs
        hashed_skill = hash_ids_in_response(skill_dict)

        return success_response(
            data=hashed_skill,
            message=f"Skill '{skill_dict['name']}' retrieved successfully",
        )

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error fetching skill {skill_id}: {str(e)}")
        return error_response(
            message=f"Error fetching skill: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@skill_router.get("/admin/skill-question-counts")
async def get_skill_question_counts_endpoint():
    """Get question counts for each skill"""
    try:
        count_list = get_skill_question_counts()

        return success_response(
            data=count_list,
            message="Skill question counts retrieved successfully",
        )
    except Exception as e:
        error(f"Error getting skill question counts: {str(e)}")
        return error_response(
            message=f"Error getting skill question counts: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@skill_router.post("/admin/generate-skill-questions")
async def generate_skill_questions(request: QuestionGenerationTaskRequest):
    """Generate questions for a specific skill using Dapr Pub/Sub"""
    try:
        skill_id = request.skill_id
        skill_name = request.skill_name
        skill_description = request.skill_description

        # Validate inputs
        validation_error = _validate_skill_generation_request(skill_id, skill_name, skill_description)
        if validation_error:
            return validation_error

        # Process skill ID
        skill_id, process_error = _process_skill_id(skill_id, skill_name)
        if process_error:
            return process_error

        # Validate environment configuration
        env_error = _validate_environment_config()
        if env_error:
            return env_error

        # Get current question count
        existing_count, count_error = _get_skill_question_count(skill_id)
        if count_error:
            return count_error

        info(f"Publishing question generation task for skill: {skill_name} (ID: {skill_id})")

        # Create task payload
        task_data = {
            "skill_id": skill_id,
            "skill_name": skill_name,
            "skill_description": skill_description,
            "task_id": f"skill_{skill_id}_{int(time.time())}",
        }

        # Try to publish to Dapr Pub/Sub, fallback to direct worker call
        dapr_success = await _publish_to_dapr(task_data)

        # Fallback: Direct call to worker if Dapr failed
        if not dapr_success:
            await _publish_to_worker(task_data)

        info(f"Successfully published question generation task for skill: {skill_name}")

        return success_response(
            data={
                "task_id": task_data["task_id"],
                "current_question_count": existing_count,
                "status": "task_started",
            },
            message=f"Question generation task started for skill: {skill_name}. "
            f"Currently has {existing_count} questions. Check back in a few minutes for new questions.",
        )

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error generating questions for skill: {str(e)}", exc_info=True)
        raise_http_exception(
            status_code=500,
            detail=f"Unexpected error during question generation: {str(e)}. "
            f"Please try again or contact support if the problem persists.",
        )


@skill_router.get("/admin/question-generation-status/{skill_id}")
async def get_question_generation_status(skill_id: str):
    """Get the current question count for a skill (to check generation progress)"""
    try:
        skill_dict = get_skill_by_id(int(skill_id))
        if not skill_dict:
            raise_http_exception(status_code=404, detail=f"Skill with ID {skill_id} not found")

        question_count = skill_dict.get("question_count", 0)
        skill_name = skill_dict.get("name", "Unknown")

        return success_response(
            data={
                "skill_id": skill_id,
                "skill_name": skill_name,
                "question_count": question_count,
                "status": "completed" if question_count > 0 else "pending",
            },
            message="Question generation status retrieved successfully",
        )

    except Exception as e:
        error(f"Error getting question generation status: {str(e)}")
        raise_http_exception(status_code=500, detail=f"Error getting question generation status: {str(e)}")


@skill_router.post("/admin/suggest-skill-description")
async def suggest_skill_description(request: SuggestSkillDescriptionRequest):
    """Suggest a skill description using LLM"""
    try:
        # Load the prompt template from prompt.yaml file
        description_prompt_template = await load_yaml_prompt("description_prompt")
        if not description_prompt_template:
            error("Failed to load description prompt template.")
            return success_response(
                data={
                    "description": "Failed to load prompt template. Please try again or enter manually.",
                    "level": "intermediate",
                    "level_color": "yellow",
                },
                message="Failed to load prompt template",
            )

        # Replace variables in the template
        template = Template(description_prompt_template)
        variables = {
            "skill_name": request.skill_name,
            "description": request.existing_description or f"A skill related to {request.skill_name}",
        }

        prompt = template.substitute(variables)

        # Get the model ID from environment variables
        model_id = os.getenv("MODEL_ID")

        # Query the model directly using the ollama_client
        response = await query_model(prompt, model_id)

        # Extract the description and level from the response
        debug(f"Full response: {response}")

        if response and "choices" in response and len(response["choices"]) > 0:
            # Extract the content from the first choice
            choice = response["choices"][0]
            if "message" in choice and "content" in choice["message"]:
                response_text = choice["message"]["content"].strip()
            elif "text" in choice:
                response_text = choice["text"].strip()
            else:
                error(f"Unexpected response structure: {response}")
                return success_response(
                    data={
                        "description": "Failed to extract content from API response. "
                        "Please try again or enter manually.",
                        "level": "intermediate",
                        "level_color": "yellow",
                    },
                    message="Failed to extract content from API response",
                )

            # Parse the response to extract improved description and level
            improved_description_match = re.search(r"Improved Description:\s*(.*?)(?:\n|$)", response_text)
            level_match = re.search(r"Level:\s*(.*?)(?:\n|$)", response_text)

            if improved_description_match:
                description = improved_description_match.group(1).strip()
            else:
                # If the specific format isn't found, use the whole response as the description
                description = response_text

            level = level_match.group(1).strip() if level_match else "intermediate"

            # Add color formatting to the level
            level_color = {
                "easy": "green",
                "intermediate": "yellow",
                "advanced": "orange",
            }.get(
                level.lower(), "yellow"
            )  # Default to yellow if level is not recognized

            return success_response(
                data={
                    "description": description,
                    "level": level,
                    "level_color": level_color,
                },
                message="Successfully generated skill description",
            )
        else:
            error(f"Invalid response structure: {response}")
            return success_response(
                data={
                    "description": "Failed to generate description. Please try again or enter manually.",
                    "level": "intermediate",
                    "level_color": "yellow",
                },
                message="Failed to generate description",
            )
    except Exception as e:
        error(f"Error suggesting skill description: {str(e)}")
        return error_response(
            message=f"Error generating skill description: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@skill_router.get("/admin/skill-questions/{skill_id}")
async def get_skill_questions(skill_id: str):
    """Get questions for a specific skill"""
    try:
        # Try to decode if it's a hash, otherwise treat as integer
        actual_id = skill_id
        if not skill_id.isdigit():
            decoded_id = decode_skill_id(skill_id)
            if decoded_id is not None:
                actual_id = decoded_id
            else:
                return error_response(
                    message=f"Invalid skill ID format: {skill_id}",
                    code=status.HTTP_400_BAD_REQUEST,
                    error_type="BadRequest",
                )
        else:
            actual_id = int(skill_id)

        # Use manager function to get skill and questions
        skill_dict, questions, total = get_skill_questions_paginated(actual_id, 1000, 0)  # Get all questions

        if not skill_dict:
            return error_response(
                message=f"Skill with ID {skill_id} not found",
                code=status.HTTP_404_NOT_FOUND,
                error_type="NotFound",
            )

        # Return empty questions array instead of 404 when skill exists but has no questions
        response_data = {
            "skill_id": actual_id,
            "skill_name": skill_dict["name"],
            "questions": questions,
        }

        # Transform response to include hashed IDs
        hashed_data = hash_ids_in_response(response_data)

        return success_response(
            data=hashed_data,
            message=f"Retrieved {len(questions)} questions for skill '{skill_dict['name']}'",
        )

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error fetching questions for skill {skill_id}: {str(e)}")
        return error_response(
            message=f"Error fetching questions for skill: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


# Skill performance endpoints
@skill_router.get("/admin/users/{user_id}/skill-performance")
async def get_user_skill_performance(user_id: int):
    """
    Get skill performance data for a specific user

    Returns skill-based metrics including:
    - Total questions answered per skill
    - Correct answers per skill
    - Total score per skill
    - Average score per skill
    - Accuracy percentage per skill
    """
    try:
        skill_performance = get_user_skill_performance_detailed(user_id)

        return success_response(
            data=skill_performance,
            message=f"Skill performance data retrieved successfully for user {user_id}",
        )
                cur.execute(
                    """
                    SELECT
                        u.id AS user_id,
                        u.display_name,
                        s.id AS skill_id,
                        s.name AS skill_name,
                        COUNT(ua.id) AS total_questions_answered,
                        SUM(CASE WHEN ua.is_correct THEN 1 ELSE 0 END) AS correct_answers,
                        SUM(ua.score) AS total_score,
                        ROUND(AVG(ua.score)::numeric, 2) AS avg_score,
                        ROUND(
                            (SUM(CASE WHEN ua.is_correct THEN 1 ELSE 0 END)::decimal / COUNT(ua.id)) * 100,
                            2
                        ) AS accuracy_percentage,

                        -- Easy level correct/incorrect
                        SUM(CASE
                            WHEN q.level = 'easy' AND ua.is_correct
                            THEN 1 ELSE 0
                        END) AS easy_correct,
                        SUM(CASE
                            WHEN q.level = 'easy' AND ua.is_correct = FALSE
                            THEN 1 ELSE 0
                        END) AS easy_incorrect,

                        -- Intermediate level correct/incorrect
                        SUM(CASE
                            WHEN q.level = 'intermediate' AND ua.is_correct
                            THEN 1 ELSE 0
                        END) AS intermediate_correct,
                        SUM(CASE
                            WHEN q.level = 'intermediate' AND ua.is_correct = FALSE
                            THEN 1 ELSE 0
                        END) AS intermediate_incorrect,

                        -- Advanced level correct/incorrect
                        SUM(CASE
                            WHEN q.level = 'advanced' AND ua.is_correct
                            THEN 1 ELSE 0
                        END) AS advanced_correct,
                        SUM(CASE
                            WHEN q.level = 'advanced' AND ua.is_correct = FALSE
                            THEN 1 ELSE 0
                        END) AS advanced_incorrect

                    FROM user_answers ua
                    JOIN sessions sess ON ua.session_id = sess.id
                    JOIN users u ON sess.user_id = u.id
                    JOIN questions q ON ua.question_id = q.que_id
                    JOIN skills s ON q.skill_id = s.id
                    WHERE u.id = %s
                    GROUP BY u.id, u.display_name, s.id, s.name
                    ORDER BY s.name
                    """,
                    (user_id,),
                )
                skill_performance = [dict(row) for row in cur.fetchall()]

                # Convert numeric values to appropriate types for JSON serialization
                for skill in skill_performance:
                    skill["total_questions_answered"] = int(skill["total_questions_answered"])
                    skill["correct_answers"] = int(skill["correct_answers"])
                    skill["total_score"] = float(skill["total_score"])
                    skill["avg_score"] = float(skill["avg_score"])
                    skill["accuracy_percentage"] = float(skill["accuracy_percentage"])

                return success_response(
                    data=skill_performance,
                    message=f"Skill performance data retrieved successfully for user {user_id}",
                )

    except Exception as e:
        error(f"Error fetching skill performance for user {user_id}: {str(e)}")
        return error_response(
            message=f"Error fetching skill performance data: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@skill_router.get("/admin/reports/skillwise-heatmap")
async def get_skillwise_heatmap():
    """
    Get skill performance data for all users in a format suitable for a heatmap

    Returns a matrix of user-skill performance data with accuracy percentages
    """
    heatmap_data = get_skillwise_heatmap_data()

    if heatmap_data is None:
        return error_response(
            message="Error fetching skillwise heatmap data",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )

    return success_response(
        data=heatmap_data,
        message="Skillwise heatmap data retrieved successfully",
    )


@skill_router.get("/user/{email}/skill-performance")
async def get_user_skill_performance_by_email(email: str):
    """
    Get skill performance data for a specific user identified by email

    Returns skill-based metrics including:
    - Total questions answered per skill
    - Correct answers per skill
    - Total score per skill
    - Average score per skill
    - Accuracy percentage per skill
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # First, get the user's internal ID
                cur.execute(
                    """
                    SELECT id FROM users WHERE email = %s
                    """,
                    (email,),
                )
                user_result = cur.fetchone()
                if not user_result:
                    return error_response(
                        message=f"User with email {email} not found",
                        code=status.HTTP_404_NOT_FOUND,
                        error_type="NotFound",
                    )
                user_id = user_result["id"]

                cur.execute(
                    """
                    SELECT
                        u.id AS user_id,
                        u.display_name,
                        s.id AS skill_id,
                        s.name AS skill_name,
                        COUNT(ua.id) AS total_questions_answered,
                        SUM(CASE WHEN ua.is_correct THEN 1 ELSE 0 END) AS correct_answers,
                        SUM(ua.score) AS total_score,
                        ROUND(AVG(ua.score)::numeric, 2) AS avg_score,
                        ROUND(
                            (SUM(CASE WHEN ua.is_correct THEN 1 ELSE 0 END)::decimal / COUNT(ua.id)) * 100,
                            2
                        ) AS accuracy_percentage,

                        -- Easy level correct/incorrect
                        SUM(CASE
                            WHEN q.level = 'easy' AND ua.is_correct
                            THEN 1 ELSE 0
                        END) AS easy_correct,
                        SUM(CASE
                            WHEN q.level = 'easy' AND ua.is_correct = FALSE
                            THEN 1 ELSE 0
                        END) AS easy_incorrect,

                        -- Intermediate level correct/incorrect
                        SUM(CASE
                            WHEN q.level = 'intermediate' AND ua.is_correct
                            THEN 1 ELSE 0
                        END) AS intermediate_correct,
                        SUM(CASE
                            WHEN q.level = 'intermediate' AND ua.is_correct = FALSE
                            THEN 1 ELSE 0
                        END) AS intermediate_incorrect,

                        -- Advanced level correct/incorrect
                        SUM(CASE
                            WHEN q.level = 'advanced' AND ua.is_correct
                            THEN 1 ELSE 0
                        END) AS advanced_correct,
                        SUM(CASE
                            WHEN q.level = 'advanced' AND ua.is_correct = FALSE
                            THEN 1 ELSE 0
                        END) AS advanced_incorrect

                    FROM user_answers ua
                    JOIN sessions sess ON ua.session_id = sess.id
                    JOIN users u ON sess.user_id = u.id
                    JOIN questions q ON ua.question_id = q.que_id
                    JOIN skills s ON q.skill_id = s.id
                    WHERE u.id = %s
                    GROUP BY u.id, u.display_name, s.id, s.name
                    ORDER BY s.name
                    """,
                    (user_id,),
                )

                skills = [dict(row) for row in cur.fetchall()]

                if not skills:
                    # Return a placeholder for no data
                    return success_response(
                        data={
                            "user_id": user_id,
                            "skills": [
                                {
                                    "skill_id": 0,
                                    "skill_name": "No Data Available",
                                    "total_questions_answered": 0,
                                    "correct_answers": 0,
                                    "total_score": 0,
                                    "average_score": 0,
                                    "accuracy_percentage": 0,
                                }
                            ],
                        },
                        message="No skill performance data available for this user",
                    )

                return success_response(
                    data={"user_id": user_id, "skills": skills},
                    message="User skill performance retrieved successfully",
                )

    except Exception as e:
        error(f"Error fetching skill performance for user {email}: {str(e)}")
        return error_response(
            message=f"Error fetching skill performance data: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )
