"""
Assessment-related database operations for the quiz/assessment management system.

This module contains all assessment-related database functions that were separated
from the main db_manager.py for better code organization.
"""

import datetime
import json
import os
from typing import List, Optional, Tuple

import psycopg2
import psycopg2.extras

from ..config.db_config import DATABASE_CONFIG
from ..utils.logger import error, log_database_error


def validate_skills_exist(skill_ids: List[int]) -> Tuple[bool, List[int]]:
    """
    Validate that all skill IDs exist in the database.

    Args:
        skill_ids: List of skill IDs to validate

    Returns:
        Tuple of (all_exist: bool, missing_skills: List[int])
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                placeholders = ",".join(["%s"] * len(skill_ids))
                cur.execute(f"SELECT id FROM skills WHERE id IN ({placeholders})", skill_ids)
                existing_skills = [row[0] for row in cur.fetchall()]

                missing_skills = list(set(skill_ids) - set(existing_skills))
                return len(missing_skills) == 0, missing_skills

    except Exception as e:
        log_database_error("select", "skills", e, skill_ids=skill_ids)
        return False, skill_ids


def create_assessment_with_skills(
    assessment_name: str, assessment_description: str, question_selection_mode: str, duration: int, skill_ids: List[int]
) -> Optional[int]:
    """
    Create assessment in database with skill associations and return assessment ID.

    Args:
        assessment_name: Name of the assessment
        assessment_description: Description of the assessment
        question_selection_mode: How questions are selected ('fixed' or 'dynamic')
        duration: Duration in minutes
        skill_ids: List of skill IDs to associate with the assessment

    Returns:
        Assessment ID if successful, None if failed
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                # Create the assessment
                cur.execute(
                    """
                    INSERT INTO assessments (
                        name, description, is_final, total_questions,
                        question_selection_mode, composition, duration_minutes
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s)
                    RETURNING id
                    """,
                    (
                        assessment_name,
                        assessment_description,
                        False,  # is_final is no longer relevant but kept for DB compatibility
                        30,  # Default total_questions
                        question_selection_mode,
                        json.dumps({"basic": 10, "intermediate": 10, "advanced": 10}),
                        duration,
                    ),
                )
                assessment_id = cur.fetchone()[0]

                # Create skill associations
                for skill_id in skill_ids:
                    cur.execute(
                        """INSERT INTO assessment_skills
                        (assessment_id, skill_id)
                        VALUES (%s, %s) ON CONFLICT DO NOTHING""",
                        (assessment_id, skill_id),
                    )

                conn.commit()
                return assessment_id

    except Exception as e:
        log_database_error("insert", "assessments", e, assessment_name=assessment_name)
        return None


def get_assessment_details(assessment_id: int) -> Optional[dict]:
    """
    Get assessment details including question selection mode.

    Args:
        assessment_id: The assessment ID

    Returns:
        Dictionary with assessment details or None if not found
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    "SELECT id, question_selection_mode FROM assessments WHERE id = %s",
                    (assessment_id,),
                )
                result = cur.fetchone()
                return dict(result) if result else None

    except Exception as e:
        log_database_error("select", "assessments", e, assessment_id=assessment_id)
        return None


def get_assessments_paginated(limit: int, offset: int) -> Tuple[List[dict], int]:
    """
    Get all assessments with pagination.

    Args:
        limit: Maximum number of items per page
        offset: Starting position

    Returns:
        Tuple of (assessments_list, total_count)
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # Get total count first
                cur.execute("SELECT COUNT(*) FROM assessments")
                total = cur.fetchone()[0]

                # Get paginated results
                cur.execute(
                    """
                    SELECT id, name, description, is_final, total_questions,
                           question_selection_mode, composition, duration_minutes, created_at
                    FROM assessments
                    ORDER BY created_at DESC
                    LIMIT %s OFFSET %s
                    """,
                    (limit, offset),
                )
                assessments = [dict(row) for row in cur.fetchall()]

                # Convert datetime objects to ISO format strings
                for assessment in assessments:
                    if assessment["created_at"]:
                        assessment["created_at"] = assessment["created_at"].isoformat()

                return assessments, total

    except Exception as e:
        log_database_error("select", "assessments", e, limit=limit, offset=offset)
        return [], 0


def get_single_assessment_by_id(assessment_id: int) -> Optional[dict]:
    """
    Get a single assessment by ID for the quiz link page.

    Args:
        assessment_id: The assessment ID

    Returns:
        Dictionary with assessment details or None if not found
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    """
                    SELECT id, name, description, is_final, total_questions,
                           question_selection_mode, composition, duration_minutes, created_at
                    FROM assessments
                    WHERE id = %s
                    """,
                    (assessment_id,),
                )
                assessment = cur.fetchone()
                return dict(assessment) if assessment else None

    except Exception as e:
        log_database_error("select", "assessments", e, assessment_id=assessment_id)
        return None


def get_assessments_with_sessions() -> List[dict]:
    """
    Get only assessments that have existing sessions.

    Returns:
        List of assessments with session counts
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    """
                    SELECT DISTINCT a.id, a.name, a.description, a.is_final,
                           a.total_questions, a.question_selection_mode,
                           a.composition, a.created_at,
                           COUNT(s.id) as session_count
                    FROM assessments a
                    INNER JOIN sessions s ON a.id = s.assessment_id
                    GROUP BY a.id, a.name, a.description, a.is_final,
                             a.total_questions, a.question_selection_mode,
                             a.composition, a.created_at
                    ORDER BY a.created_at DESC
                    """
                )
                assessments = [dict(row) for row in cur.fetchall()]

                # Convert datetime objects to ISO format strings
                for assessment in assessments:
                    if assessment["created_at"]:
                        assessment["created_at"] = assessment["created_at"].isoformat()

                return assessments

    except Exception as e:
        log_database_error("select", "assessments", e)
        return []


def get_fixed_assessment_questions(assessment_id: int) -> Optional[dict]:
    """
    Get the assigned questions for a fixed assessment.

    Args:
        assessment_id: The assessment ID

    Returns:
        Dictionary with assessment and question details or None if not found/not fixed
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # First check if the assessment exists and is a fixed assessment
                cur.execute(
                    "SELECT id, name, question_selection_mode FROM assessments WHERE id = %s",
                    (assessment_id,),
                )
                assessment = cur.fetchone()

                if not assessment:
                    return None

                if assessment["question_selection_mode"] != "fixed":
                    return {
                        "error": "not_fixed",
                        "message": f"Assessment with ID {assessment_id} is not a fixed assessment",
                    }

                # Get the assigned questions for this assessment
                cur.execute(
                    "SELECT question_id FROM assessment_questions WHERE assessment_id = %s ORDER BY id",
                    (assessment_id,),
                )
                results = cur.fetchall()

                # Extract question IDs
                question_ids = [row[0] for row in results]

                return {
                    "assessment_id": assessment_id,
                    "assessment_name": assessment["name"],
                    "question_ids": question_ids,
                }

    except Exception as e:
        log_database_error("select", "assessment_questions", e, assessment_id=assessment_id)
        return None


def get_user_assessments_by_id(user_id: int) -> Optional[dict]:
    """
    Get all assessments taken by a specific user with their scores.

    Args:
        user_id: The user ID

    Returns:
        Dictionary with user info and assessments or None if user not found
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # Get user details (excluding sensitive data)
                cur.execute(
                    """
                    SELECT id, external_id, display_name
                    FROM users
                    WHERE id = %s
                    """,
                    (user_id,),
                )
                user = cur.fetchone()

                if not user:
                    return None

                user_dict = dict(user)
                if not user_dict["display_name"]:
                    user_dict["display_name"] = user_dict["external_id"]

                # Get all sessions for this user with assessment details and scores
                cur.execute(
                    """
                    SELECT
                        s.id AS session_id,
                        s.created_at AS session_created,
                        s.completed_at AS session_completed,
                        a.id AS assessment_id,
                        a.name AS assessment_name,
                        a.question_selection_mode AS mode,
                        a.description AS assessment_description,
                        s.score,
                        s.status,

                        COALESCE(SUM(CASE WHEN q.level = 'easy' THEN 1 ELSE 0 END), 0) AS easy_count,
                        COALESCE(SUM(CASE WHEN q.level = 'intermediate' THEN 1 ELSE 0 END), 0) AS intermediate_count,
                        COALESCE(SUM(CASE WHEN q.level = 'advanced' THEN 1 ELSE 0 END), 0) AS advanced_count

                    FROM sessions s
                    JOIN assessments a ON s.assessment_id = a.id
                    LEFT JOIN assessment_questions aq ON a.id = aq.assessment_id
                    LEFT JOIN questions q ON aq.question_id = q.que_id

                    WHERE s.user_id = %s

                    GROUP BY
                        s.id, s.created_at, s.completed_at, s.score, s.status,
                        a.id, a.name, a.question_selection_mode, a.description

                    ORDER BY
                        (s.status = 'completed') DESC,
                        s.created_at DESC;

                    """,
                    (user_id,),
                )
                sessions = [dict(row) for row in cur.fetchall()]

                # Convert datetime objects to ISO format strings
                for session in sessions:
                    if session["session_created"]:
                        session["session_created"] = session["session_created"].isoformat()
                    if session["session_completed"]:
                        session["session_completed"] = session["session_completed"].isoformat()

                return {"user": user_dict, "assessments": sessions}

    except Exception as e:
        log_database_error("select", "users", e, user_id=user_id)
        return None


def get_user_assessments_by_email(email: str) -> Optional[dict]:
    """
    Get all assessments taken by a specific user identified by email.

    Args:
        email: The user's email address

    Returns:
        Dictionary with user info and assessments or None if user not found
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # Get user details (excluding sensitive data from response)
                cur.execute(
                    """
                    SELECT id, external_id, display_name
                    FROM users
                    WHERE email = %s
                    """,
                    (email,),
                )
                user = cur.fetchone()

                if not user:
                    return None

                user_dict = dict(user)
                if not user_dict["display_name"]:
                    user_dict["display_name"] = user_dict["external_id"]

                user_id = user_dict["id"]

                # Get all sessions for this user with assessment details and scores
                cur.execute(
                    """
                    SELECT
                        s.id AS session_id,
                        s.created_at AS session_created,
                        s.completed_at AS session_completed,
                        a.id AS assessment_id,
                        a.name AS assessment_name,
                        a.question_selection_mode AS mode,
                        a.description AS assessment_description,
                        s.score,
                        s.status,

                        COALESCE(SUM(CASE WHEN q.level = 'easy' THEN 1 ELSE 0 END), 0) AS easy_count,
                        COALESCE(SUM(CASE WHEN q.level = 'intermediate' THEN 1 ELSE 0 END), 0) AS intermediate_count,
                        COALESCE(SUM(CASE WHEN q.level = 'advanced' THEN 1 ELSE 0 END), 0) AS advanced_count

                    FROM sessions s
                    JOIN assessments a ON s.assessment_id = a.id
                    LEFT JOIN assessment_questions aq ON a.id = aq.assessment_id
                    LEFT JOIN questions q ON aq.question_id = q.que_id

                    WHERE s.user_id = %s

                    GROUP BY
                        s.id, s.created_at, s.completed_at, s.score, s.status,
                        a.id, a.name, a.question_selection_mode, a.description

                    ORDER BY
                        (s.status = 'completed') DESC,
                        s.created_at DESC;

                    """,
                    (user_id,),
                )
                sessions = [dict(row) for row in cur.fetchall()]

                # Convert datetime objects to ISO format strings
                for session in sessions:
                    if session["session_created"]:
                        session["session_created"] = session["session_created"].isoformat()
                    if session["session_completed"]:
                        session["session_completed"] = session["session_completed"].isoformat()

                return {"user": user_dict, "assessments": sessions}

    except Exception as e:
        log_database_error("select", "users", e, email=email)
        return None


def add_questions_to_fixed_assessment(assessment_id: int, question_ids: List[int]) -> Tuple[bool, str]:
    """
    Add questions to a fixed assessment.

    Args:
        assessment_id: The assessment ID
        question_ids: List of question IDs to add

    Returns:
        Tuple of (success: bool, message: str)
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                # First, validate the assessment exists and is a fixed assessment
                cur.execute(
                    "SELECT id, question_selection_mode FROM assessments WHERE id = %s",
                    (assessment_id,),
                )
                assessment = cur.fetchone()
                if not assessment:
                    return False, f"Assessment with ID {assessment_id} not found"

                if assessment[1] != "fixed":
                    return False, f"Assessment with ID {assessment_id} is not a fixed assessment"

                # Clear existing questions for this assessment
                cur.execute("DELETE FROM assessment_questions WHERE assessment_id = %s", (assessment_id,))

                # Add the new questions
                for question_id in question_ids:
                    cur.execute(
                        "INSERT INTO assessment_questions (assessment_id, question_id) VALUES (%s, %s)",
                        (assessment_id, question_id),
                    )

                conn.commit()
                return True, f"Successfully added {len(question_ids)} questions to fixed assessment"

    except Exception as e:
        log_database_error("insert", "assessment_questions", e, assessment_id=assessment_id)
        return False, f"Error adding questions to assessment: {str(e)}"


def get_assessment_description(assessment_id):
    """
    Get the description of an assessment by its ID.

    Args:
        assessment_id (int): The ID of the assessment.

    Returns:
        str: The description of the assessment, or empty string if not found.
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    "SELECT description FROM assessments WHERE id = %s",
                    (assessment_id,),
                )
                desc_row = cur.fetchone()
                if desc_row:
                    return desc_row["description"]
                return ""
    except Exception as e:
        log_database_error("select", "assessments", e, assessment_id=assessment_id)
        return ""


def get_performance_level(obtained_score, total_score):
    """
    Determine the performance level based on the obtained score percentage.

    Args:
        obtained_score (int): The score obtained by the user.
        total_score (int): The maximum possible score.

    Returns:
        str: The performance level (Fail, Basic, Acceptable, Exceed Expectation, OUTSTANDING).
    """
    if obtained_score < 0 or obtained_score > total_score:
        return "Invalid score"

    percentage = (obtained_score / total_score) * 100 if total_score > 0 else 0

    levels = [
        (0, "Fail"),
        (33, "Basic"),
        (62, "Acceptable"),
        (85, "Exceed Expectation"),
        (100, "OUTSTANDING"),
    ]

    # Corrected logic for level determination
    if percentage == 0 and obtained_score == 0:  # Handles the case where total_score might be 0 or obtained_score is 0
        return "Fail"

    performance = "Fail"  # Default
    for threshold, level in levels:
        if percentage >= threshold:  # Check if percentage is greater than or equal to threshold
            performance = level
        else:  # if it's less, then the previous level was correct
            break

    # Special case for 100%
    if percentage == 100:
        performance = "OUTSTANDING"

    return performance


def calculate_total_score(easy_attempted, intermediate_attempted, advanced_attempted):
    """
    Calculate total score based on attempted questions in different difficulty levels.

    Args:
        easy_attempted (int): Number of easy questions attempted.
        intermediate_attempted (int): Number of intermediate questions attempted.
        advanced_attempted (int): Number of advanced questions attempted.

    Returns:
        int: Total score based on the formula.
    """
    total_score = (easy_attempted * 1) + (intermediate_attempted * 2) + (advanced_attempted * 3)
    return total_score


def calculate_percentage(obtained, total, quiz_type):
    """Helper function to calculate the percentage only for 'final' quizzes."""
    if quiz_type == "final":
        return f"{round((obtained / total) * 100, 2)}%" if total > 0 else "0%"
    return ""


def calculate_total_score_for_assessment(assessment_id, session_id=None):
    """
    Calculate the correct total possible score for an assessment based on its mode.

    For fixed assessments: Calculate based on pre-selected questions
    For dynamic assessments: Calculate based on questions actually attempted in the session

    Args:
        assessment_id (int): The assessment ID
        session_id (int, optional): The session ID (required for dynamic assessments)

    Returns:
        int: The total possible score for this assessment
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # Get assessment mode
                cur.execute(
                    "SELECT question_selection_mode FROM assessments WHERE id = %s",
                    (assessment_id,),
                )
                assessment = cur.fetchone()
                if not assessment:
                    return 0

                mode = assessment["question_selection_mode"]

                if mode == "fixed":
                    # For fixed assessments, calculate based on pre-selected questions
                    cur.execute(
                        """SELECT q.level, COUNT(*) as count
                           FROM assessment_questions aq
                           JOIN questions q ON aq.question_id = q.que_id
                           WHERE aq.assessment_id = %s
                           GROUP BY q.level""",
                        (assessment_id,),
                    )
                    level_counts = cur.fetchall()

                    total_score = 0
                    for row in level_counts:
                        level = row["level"]
                        count = row["count"]
                        if level == "easy":
                            total_score += count * 1
                        elif level == "intermediate":
                            total_score += count * 2
                        elif level == "advanced":
                            total_score += count * 3

                    return total_score

                else:  # dynamic mode
                    # For dynamic assessments, calculate based on questions actually attempted
                    if not session_id:
                        return 0

                    cur.execute(
                        """SELECT q.level, COUNT(*) as count
                           FROM user_answers ua
                           JOIN questions q ON ua.question_id = q.que_id
                           WHERE ua.session_id = %s
                           GROUP BY q.level""",
                        (session_id,),
                    )
                    level_counts = cur.fetchall()

                    total_score = 0
                    for row in level_counts:
                        level = row["level"]
                        count = row["count"]
                        if level == "easy":
                            total_score += count * 1
                        elif level == "intermediate":
                            total_score += count * 2
                        elif level == "advanced":
                            total_score += count * 3

                    return total_score

    except Exception as e:
        log_database_error("calculate", "user_answers", e, assessment_id=assessment_id)
        return 0


def get_performance_level_with_correct_total(obtained_score, assessment_id, session_id=None):
    """
    Calculate performance level using the correct total score based on assessment mode.

    Args:
        obtained_score (int): Score obtained by the user
        assessment_id (int): The assessment ID
        session_id (int, optional): The session ID (required for dynamic assessments)

    Returns:
        str: Performance level
    """
    total_score = calculate_total_score_for_assessment(assessment_id, session_id)
    if total_score == 0:
        return "Fail"

    return get_performance_level(obtained_score, total_score)


def assessment_report_by_user(user_name: str):
    """
    Retrieve user assessment data and generate a detailed report including
    both individual assessments and aggregated score summaries.

    Args:
        user_name (str): The username to filter the assessment data.

    Returns:
        dict: A dictionary containing:
            - 'base_report': A list of dictionaries with detailed user assessments.
            - 'score_report': A list of dictionaries with aggregated user scores.

    Raises:
        Exception: If any error occurs while fetching data from the database.

    Notes:
        - The function retrieves both individual question-level details
        and aggregated performance metrics.
        - The 'base_report' provides a breakdown of each attempted question.
        - The 'score_report' summarizes scores, attempts, and performance levels
        per quiz topic and date.
        - The `performance_level` is determined based on the user's obtained score.
    """

    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                # Query for base report (detailed assessments)
                cur.execute(
                    """
                    SELECT
                        id, user_id, topic, level, quiz_type, que_id, question, options,
                        user_answer, correct_answer, result, score, time
                    FROM
                        user_assessment
                    WHERE
                        user_id = %s
                    ORDER BY
                        topic ASC, time ASC;
                    """,
                    (user_name,),
                )
                base_rows = cur.fetchall()
                base_report = (
                    [
                        {
                            "id": row[0],
                            "user_id": row[1],
                            "topic": row[2],
                            "level": row[3],
                            "quiz_type": row[4],
                            "que_id": row[5],
                            "question": row[6],
                            "options": row[7],
                            "user_answer": row[8],
                            "correct_answer": row[9],
                            "result": row[10],
                            "score": row[11],
                            "time": row[12],
                        }
                        for row in base_rows
                    ]
                    if base_rows
                    else []
                )

                # Query for score report (aggregated scores)
                cur.execute(
                    """SELECT
                        user_id,
                        topic,
                        quiz_type,
                        SUM(score) AS obtained_score,
                        SUM(CASE WHEN level = 'easy'
                        THEN 1 ELSE 0 END) AS easy_attempted,
                        SUM(CASE WHEN result = 'Correct' AND level = 'easy'
                        THEN 1 ELSE 0 END) AS easy_correct,
                        SUM(CASE WHEN level = 'intermediate'
                        THEN 1 ELSE 0 END) AS intermediate_attempted,
                        SUM(CASE WHEN result = 'Correct' AND level = 'intermediate'
                        THEN 1 ELSE 0 END) AS intermediate_correct,
                        SUM(CASE WHEN level = 'advanced'
                        THEN 1 ELSE 0 END) AS advanced_attempted,
                        SUM(CASE WHEN result = 'Correct' AND level = 'advanced'
                        THEN 1 ELSE 0 END) AS advanced_correct,
                        DATE(time) AS assessment_date
                    FROM
                        user_assessment
                    WHERE
                        user_id = %s
                    GROUP BY
                        user_id, topic, DATE(time), quiz_type
                    ORDER BY
                        quiz_type;
                    """,
                    (user_name,),
                )
                score_rows = cur.fetchall()

                score_report = []
                if score_rows:
                    for row in score_rows:
                        total_score = calculate_total_score(row[4], row[6], row[8])

                        score_report.append(
                            {
                                "user_id": row[0],
                                "topic": row[1],
                                "total_score": total_score,
                                "obtained_score": row[3],
                                "quiz_type": row[2],
                                "percentage": calculate_percentage(row[3], total_score, row[2]),
                                "performance_level": get_performance_level(row[3], total_score),
                                "easy_attempted": row[4],
                                "easy_correct": row[5],
                                "intermediate_attempted": row[6],
                                "intermediate_correct": row[7],
                                "advanced_attempted": row[8],
                                "advanced_correct": row[9],
                                "assessment_date": row[10],
                            }
                        )

                return {"base_report": base_report, "score_report": score_report}

    except Exception as e:
        log_database_error("select", "user_assessment", e, user_name=user_name)
        return {"base_report": [], "score_report": []}


def assessment_report_by_topic(topic: str, quiz_type: str):
    """
    Retrieve user assessment data and optionally include score reports
    based on topic and quiz type.

    Args:
        topic (str): The topic to filter the assessment data.
        quiz_type (str): The type of quiz to filter the assessment data.

    Returns:
        dict: A dictionary containing 'base_report' (detailed assessments)
              and 'score_report' (aggregated user scores).

    Raises:
        ValueError: If the `quiz_type` is not valid.

    Notes:
        - The `topic` should match an existing topic in the database.
        - The `quiz_type` must match a valid quiz type ('mock' or 'final').
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                # Query for base report (detailed assessments)
                cur.execute(
                    """
                    SELECT
                        id, user_id, topic, level, quiz_type, que_id, question, options,
                        user_answer, correct_answer, result, score, time
                    FROM
                        user_assessment
                    WHERE
                        topic = %s AND quiz_type = %s
                    ORDER BY
                        user_id ASC, time ASC;
                    """,
                    (topic, quiz_type),
                )
                base_rows = cur.fetchall()
                base_report = (
                    [
                        {
                            "id": row[0],
                            "user_id": row[1],
                            "topic": row[2],
                            "level": row[3],
                            "quiz_type": row[4],
                            "que_id": row[5],
                            "question": row[6],
                            "options": row[7],
                            "user_answer": row[8],
                            "correct_answer": row[9],
                            "result": row[10],
                            "score": row[11],
                            "time": row[12],
                        }
                        for row in base_rows
                    ]
                    if base_rows
                    else []
                )

                # Query for score report (aggregated scores)
                cur.execute(
                    """
                    SELECT
                        user_id,
                        topic,
                        SUM(score) AS obtained_score,
                        SUM(CASE WHEN level = 'easy' THEN 1 ELSE 0 END) AS easy_attempted,
                        SUM(CASE WHEN result = 'Correct' AND level = 'easy'
                        THEN 1 ELSE 0 END) AS easy_correct,
                        SUM(CASE WHEN level = 'intermediate'
                        THEN 1 ELSE 0 END) AS intermediate_attempted,
                        SUM(CASE WHEN result = 'Correct' AND level = 'intermediate'
                        THEN 1 ELSE 0 END) AS intermediate_correct,
                        SUM(CASE WHEN level = 'advanced' THEN 1 ELSE 0 END) AS advanced_attempted,
                        SUM(CASE WHEN result = 'Correct' AND level = 'advanced'
                        THEN 1 ELSE 0 END) AS advanced_correct
                    FROM
                        user_assessment
                    WHERE
                        topic = %s AND quiz_type = %s
                    GROUP BY
                        user_id, topic
                    ORDER BY
                        topic
                    """,
                    (topic, quiz_type),
                )
                score_rows = cur.fetchall()

                score_report = []
                if score_rows:
                    for row in score_rows:
                        total_score = calculate_total_score(row[3], row[5], row[7])

                        score_report.append(
                            {
                                "user_id": row[0],
                                "topic": row[1],
                                "total_score": total_score,
                                "obtained_score": row[2],
                                "percentage": calculate_percentage(row[2], total_score, quiz_type),
                                "performance_level": get_performance_level(row[2], total_score),
                                "easy_attempted": row[3],
                                "easy_correct": row[4],
                                "intermediate_attempted": row[5],
                                "intermediate_correct": row[6],
                                "advanced_attempted": row[7],
                                "advanced_correct": row[8],
                            }
                        )

                return {"base_report": base_report, "score_report": score_report}

    except Exception as e:
        log_database_error("select", "user_assessment", e, topic=topic, quiz_type=quiz_type)
        return {"base_report": [], "score_report": []}


def get_assessment_by_id(assessment_id: int, include_questions: bool = True, include_answers: bool = False):
    """
    Get a single assessment with its questions and associated skills.

    Args:
        assessment_id: The ID of the assessment to retrieve
        include_questions: Whether to include available_questions in the response
        include_answers: Whether to include answers in the response (for security)

    Returns:
        A dictionary containing assessment details, or None if not found
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # First check if the assessment exists
                cur.execute(
                    """
                    SELECT id, name, description, is_final, total_questions,
                           question_selection_mode, composition, duration_minutes,
                           created_at, updated_at
                    FROM assessments
                    WHERE id = %s
                    """,
                    (assessment_id,),
                )
                assessment = cur.fetchone()
                if not assessment:
                    return None

                assessment_dict = dict(assessment)

                # Get the skill IDs associated with this assessment
                cur.execute(
                    "SELECT skill_id FROM assessment_skills WHERE assessment_id = %s",
                    (assessment_id,),
                )
                skill_rows = cur.fetchall()
                skill_ids = [row["skill_id"] for row in skill_rows]
                assessment_dict["skill_ids"] = skill_ids

                # Get skill names for display
                if skill_ids:
                    placeholders = ",".join(["%s"] * len(skill_ids))
                    cur.execute(
                        f"SELECT id, name FROM skills WHERE id IN ({placeholders})",
                        skill_ids,
                    )
                    skill_rows = cur.fetchall()
                    assessment_dict["skills"] = [{"id": row["id"], "name": row["name"]} for row in skill_rows]
                else:
                    assessment_dict["skills"] = []

                # Only include questions if requested
                if include_questions and skill_ids:
                    # Determine which column to select for answers based on include_answers flag
                    answer_column = "q.answer" if include_answers else "NULL as answer"

                    # Use parameterized query with placeholders for each skill ID
                    placeholders = ",".join(["%s"] * len(skill_ids))
                    query = f"""
                        SELECT q.que_id, q.topic, q.level, q.question, q.options, {answer_column}, q.time, q.skill_id,
                               s.name as skill_name,
                               CASE WHEN aq.question_id IS NOT NULL THEN true ELSE false END as selected
                        FROM questions q
                        JOIN skills s ON q.skill_id = s.id
                        LEFT JOIN assessment_questions aq ON q.que_id = aq.question_id AND aq.assessment_id = %s
                        WHERE q.skill_id IN ({placeholders})
                        ORDER BY q.level, q.time DESC
                    """
                    # First parameter is assessment_id, followed by all skill_ids
                    params = [assessment_id] + skill_ids
                    cur.execute(query, params)

                    all_questions = [dict(row) for row in cur.fetchall()]

                    # Only include questions in response if requested
                    if include_questions:
                        assessment_dict["available_questions"] = all_questions

                return assessment_dict

    except Exception as e:
        error(f"Error fetching assessment {assessment_id}: {str(e)}")
        return None


def get_assessment_questions_by_id(assessment_id: int):
    """
    Get all available questions for an assessment based on its associated skills.

    Args:
        assessment_id: The ID of the assessment

    Returns:
        A dictionary containing assessment questions and related information, or None if not found
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # First check if the assessment exists
                cur.execute(
                    "SELECT id, name, question_selection_mode FROM assessments WHERE id = %s",
                    (assessment_id,),
                )
                assessment = cur.fetchone()
                if not assessment:
                    return None

                # Get the skill IDs associated with this assessment
                cur.execute(
                    "SELECT skill_id FROM assessment_skills WHERE assessment_id = %s",
                    (assessment_id,),
                )
                skill_rows = cur.fetchall()
                skill_ids = [row["skill_id"] for row in skill_rows]

                if not skill_ids:
                    return {
                        "assessment_id": assessment_id,
                        "assessment_name": assessment["name"],
                        "question_selection_mode": assessment["question_selection_mode"],
                        "questions": [],
                        "counts": {
                            "easy": 0,
                            "intermediate": 0,
                            "advanced": 0,
                            "selected_easy": 0,
                            "selected_intermediate": 0,
                            "selected_advanced": 0,
                            "required_easy": int(os.getenv("EASY_QUESTIONS_COUNT", "6")),
                            "required_intermediate": int(os.getenv("INTERMEDIATE_QUESTIONS_COUNT", "6")),
                            "required_advanced": int(os.getenv("ADVANCED_QUESTIONS_COUNT", "8")),
                        },
                    }

                # Get all questions for these skills
                # Use parameterized query with placeholders for each skill ID
                placeholders = ",".join(["%s"] * len(skill_ids))
                query = f"""
                    SELECT q.que_id, q.topic, q.level, q.question, q.options, q.answer, q.time, q.skill_id,
                           s.name as skill_name,
                           CASE WHEN aq.question_id IS NOT NULL THEN true ELSE false END as selected
                    FROM questions q
                    JOIN skills s ON q.skill_id = s.id
                    LEFT JOIN assessment_questions aq ON q.que_id = aq.question_id AND aq.assessment_id = %s
                    WHERE q.skill_id IN ({placeholders})
                    ORDER BY q.level, q.time DESC
                """
                # First parameter is assessment_id, followed by all skill_ids
                params = [assessment_id] + skill_ids
                cur.execute(query, params)

                questions = [dict(row) for row in cur.fetchall()]

                # Count questions by level
                easy_count = sum(1 for q in questions if q["level"] == "easy")
                intermediate_count = sum(1 for q in questions if q["level"] == "intermediate")
                advanced_count = sum(1 for q in questions if q["level"] == "advanced")

                # Count selected questions by level
                selected_easy = sum(1 for q in questions if q["level"] == "easy" and q["selected"])
                selected_intermediate = sum(1 for q in questions if q["level"] == "intermediate" and q["selected"])
                selected_advanced = sum(1 for q in questions if q["level"] == "advanced" and q["selected"])

                # Get required counts from environment variables
                required_easy = int(os.getenv("EASY_QUESTIONS_COUNT", "6"))
                required_intermediate = int(os.getenv("INTERMEDIATE_QUESTIONS_COUNT", "6"))
                required_advanced = int(os.getenv("ADVANCED_QUESTIONS_COUNT", "8"))

                return {
                    "assessment_id": assessment_id,
                    "assessment_name": assessment["name"],
                    "question_selection_mode": assessment["question_selection_mode"],
                    "questions": questions,
                    "counts": {
                        "easy": easy_count,
                        "intermediate": intermediate_count,
                        "advanced": advanced_count,
                        "selected_easy": selected_easy,
                        "selected_intermediate": selected_intermediate,
                        "selected_advanced": selected_advanced,
                        "required_easy": required_easy,
                        "required_intermediate": required_intermediate,
                        "required_advanced": required_advanced,
                    },
                }

    except Exception as e:
        error(f"Error fetching questions for assessment {assessment_id}: {str(e)}")
        return None


def assessment_report_with_question_stats(assessment_base_name: str, quiz_type: str):
    """
    Retrieve assessment data with question-wise statistics including user attendance.

    Args:
        assessment_base_name (str): The base name of the assessment (e.g., "data engineer_22_05_2025").
        quiz_type (str): The type of quiz to filter the assessment data (not used in new structure).

    Returns:
        dict: A dictionary containing 'base_report' (question-wise stats)
              and 'score_report' (aggregated user scores).
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                # Find the assessment by name pattern
                cur.execute(
                    """
                    SELECT id, name, question_selection_mode
                    FROM assessments
                    WHERE name LIKE %s
                """,
                    (f"{assessment_base_name}%",),
                )
                assessments = cur.fetchall()

                if not assessments:
                    from ..utils.logger import warning

                    warning(f"No assessments found for pattern '{assessment_base_name}%'")
                    return {"base_report": [], "score_report": []}

                # Use the first matching assessment
                assessment_id = assessments[0][0]
                assessment_name = assessments[0][1]

                # Query for question-wise statistics using the new structure
                # Get all questions that were actually used in this assessment
                cur.execute(
                    """
                    SELECT
                        q.que_id,
                        q.skill_id,
                        s.name as skill_name,
                        q.level,
                        q.question,
                        q.options,
                        q.answer,
                        q.topic as question_topic,
                        COUNT(DISTINCT sess.user_id) as total_attended_users,
                        COUNT(CASE WHEN ua.is_correct = true THEN 1 END) as correct_answers,
                        COUNT(ua.session_id) as total_attempts,
                        ROUND(
                            (COUNT(CASE WHEN ua.is_correct = true THEN 1 END) * 100.0 /
                             NULLIF(COUNT(ua.session_id), 0)), 2
                        ) as correct_percentage
                    FROM questions q
                    LEFT JOIN skills s ON q.skill_id = s.id
                    INNER JOIN user_answers ua ON q.que_id = ua.question_id
                    INNER JOIN sessions sess ON ua.session_id = sess.id AND sess.assessment_id = %s
                    GROUP BY q.que_id, q.skill_id, s.name, q.level, q.question, q.options, q.answer, q.topic
                    ORDER BY s.name, q.level, q.question
                    """,
                    (assessment_id,),
                )
                question_rows = cur.fetchall()

                base_report = []
                if question_rows:
                    for row in question_rows:
                        base_report.append(
                            {
                                "que_id": row[0],
                                "skill_name": row[2] or "Unknown",
                                "level": row[3],
                                "question": row[4],
                                "options": row[5],
                                "correct_answer": row[6],
                                "question_topic": row[7],
                                "total_attended_users": row[8],
                                "correct_answers": row[9],
                                "total_attempts": row[10],
                                "correct_percentage": row[11] or 0.0,
                            }
                        )

                # Query for score report (aggregated scores) - using new structure
                cur.execute(
                    """
                    SELECT
                        u.external_id as user_id,
                        SUM(ua.score) AS obtained_score,
                        SUM(CASE WHEN q.level = 'easy'
                        THEN 1 ELSE 0 END) AS easy_attempted,
                        SUM(CASE WHEN ua.is_correct = true AND q.level = 'easy'
                        THEN 1 ELSE 0 END) AS easy_correct,
                        SUM(CASE WHEN q.level = 'intermediate'
                        THEN 1 ELSE 0 END) AS intermediate_attempted,
                        SUM(CASE WHEN ua.is_correct = true AND q.level = 'intermediate'
                        THEN 1 ELSE 0 END) AS intermediate_correct,
                        SUM(CASE WHEN q.level = 'advanced'
                        THEN 1 ELSE 0 END) AS advanced_attempted,
                        SUM(CASE WHEN ua.is_correct = true AND q.level = 'advanced'
                        THEN 1 ELSE 0 END) AS advanced_correct
                    FROM
                        user_answers ua
                        JOIN sessions sess ON ua.session_id = sess.id
                        JOIN users u ON sess.user_id = u.id
                        JOIN questions q ON ua.question_id = q.que_id
                    WHERE
                        sess.assessment_id = %s
                    GROUP BY
                        u.external_id
                    ORDER BY
                        u.external_id ASC;
                    """,
                    (assessment_id,),
                )
                score_rows = cur.fetchall()

                score_report = []
                if score_rows:
                    for row in score_rows:
                        total_score = calculate_total_score(row[2], row[4], row[6])

                        score_report.append(
                            {
                                "user_id": row[0],
                                "topic": assessment_name,
                                "total_score": total_score,
                                "obtained_score": row[1],
                                "percentage": calculate_percentage(row[1], total_score, quiz_type),
                                "performance_level": get_performance_level(row[1], total_score),
                                "easy_attempted": row[2],
                                "easy_correct": row[3],
                                "intermediate_attempted": row[4],
                                "intermediate_correct": row[5],
                                "advanced_attempted": row[6],
                                "advanced_correct": row[7],
                            }
                        )

                return {"base_report": base_report, "score_report": score_report}

    except Exception as e:
        log_database_error("select", "user_assessment", e, assessment_base_name=assessment_base_name)
        return {"base_report": [], "score_report": []}


# =============================================================================
# ASSESSMENT QUESTION FUNCTIONS (moved from db_manager.py)
# =============================================================================


def fetch_questions_for_fixed_assessment(assessment_id):
    """
    Fetch pre-selected questions for a fixed assessment from the `assessment_questions` table.

    Args:
        assessment_id (int): The ID of the assessment to fetch questions for.

    Returns:
        List[Dict]: A list of dictionaries, each representing a question and its attributes.

    Notes:
        - Only fetches questions that are specifically assigned to the fixed assessment.
        - Uses `psycopg2.extras.DictCursor` to return results as dictionaries.
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    """
                    SELECT q.*
                    FROM questions q
                    JOIN assessment_questions aq ON q.que_id = aq.question_id
                    WHERE aq.assessment_id = %s
                    """,
                    (assessment_id,),
                )
                rows = cur.fetchall()
                return [dict(row) for row in rows]
    except Exception as e:
        log_database_error("select", "questions", e, assessment_id=assessment_id)
        return []


def fetch_dynamic_questions_excluding_fixed(quiz_name):
    """
    Fetch questions for dynamic assessments, excluding those assigned to fixed assessments.

    Args:
        quiz_name (str): The name of the quiz to fetch questions for.

    Returns:
        List[Dict]: A list of dictionaries, each representing a question and its attributes.

    Notes:
        - Excludes questions that are assigned to any fixed assessment.
        - Uses `psycopg2.extras.DictCursor` to return results as dictionaries.
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    """SELECT q.* FROM questions q
                       WHERE q.topic = %s
                       AND q.que_id NOT IN (
                           SELECT DISTINCT aq.question_id
                           FROM assessment_questions aq
                           JOIN assessments a ON aq.assessment_id = a.id
                           WHERE a.question_selection_mode = 'fixed'
                       )""",
                    (quiz_name,),
                )
                rows = cur.fetchall()
                return [dict(row) for row in rows]
    except Exception as e:
        log_database_error("select", "questions", e, quiz_name=quiz_name)
        return []


def get_session_and_assessment_details_by_code(session_code: str):
    """
    Retrieve session and associated assessment details for a given session code.

    Args:
        session_code (str): The session code to look up.

    Returns:
        dict: A dictionary with session_id, assessment_id, assessment_name,
              is_final, user_id, session_status, remaining_time_seconds,
              and attempted_questions if found.
        None: If the session code is not found or an error occurs.
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    """
                    SELECT
                        s.id AS session_id,
                        s.user_id,
                        s.status AS session_status,
                        a.id AS assessment_id,
                        a.name AS assessment_name,
                        a.is_final,
                        s.started_at,
                        s.completed_at,
                        a.duration_minutes
                    FROM sessions s
                    JOIN assessments a ON s.assessment_id = a.id
                    WHERE s.code = %s;
                    """,
                    (session_code,),
                )
                result = cur.fetchone()
                if not result:
                    return None

                session_data = dict(result)

                # Calculate remaining time for sessions
                duration_minutes = session_data.get("duration_minutes", 30)
                total_duration_seconds = duration_minutes * 60

                if session_data["session_status"] == "in_progress" and session_data["started_at"]:
                    # Get current time in UTC
                    now = datetime.datetime.now(datetime.timezone.utc)

                    # Convert started_at to UTC if it's not already
                    started_at = session_data["started_at"]
                    if started_at.tzinfo is None:
                        started_at = started_at.replace(tzinfo=datetime.timezone.utc)

                    # Calculate elapsed time since session started
                    elapsed_time = now - started_at
                    elapsed_seconds = int(elapsed_time.total_seconds())

                    # Calculate actual active time (all elapsed time since no pause functionality)
                    active_seconds = elapsed_seconds

                    # Calculate remaining time
                    remaining_seconds = max(0, total_duration_seconds - active_seconds)

                    session_data["remaining_time_seconds"] = remaining_seconds
                else:
                    # For non-in-progress sessions, set default duration
                    session_data["remaining_time_seconds"] = total_duration_seconds

                # Get attempted questions for this session
                cur.execute(
                    """
                    SELECT question_id
                    FROM user_answers
                    WHERE session_id = %s
                    """,
                    (session_data["session_id"],),
                )
                attempted_questions = [row[0] for row in cur.fetchall()]
                session_data["attempted_questions"] = attempted_questions

                return session_data
    except Exception as e:
        log_database_error("select", "sessions", e, session_code=session_code)
        return None


def insert_quiz_creation_logs(data: list):
    """
    Insert quiz creation details into the `quiz_creation_logs` table.

    Args:
        data (list): A list of dictionaries, each containing:
            - "user_id" (str): The admin's username.
            - "assessment_description" (str): The assessment description.
            - "assessment_name" (str): The assessment name.
            - "total_questions" (int): The total number of questions in the quiz.
            - "assessment_id" (int): The ID of the created assessment.

    Raises:
        ValueError: If `data` is not a list of dictionaries.

    Notes:
        Uses the updated column names after migration 025.
    """
    if not isinstance(data, list) or not all(isinstance(entry, dict) for entry in data):
        raise ValueError("`data` must be a list of dictionaries.")

    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                for entry in data:
                    # Support both old and new column names for backward compatibility
                    assessment_name = entry.get("assessment_name") or entry.get("quiz_name")
                    assessment_description = entry.get("assessment_description") or entry.get("topic")
                    assessment_id = entry.get("assessment_id")

                    # Truncate values to fit VARCHAR(255) constraints
                    user_id = entry["user_id"][:255] if len(entry["user_id"]) > 255 else entry["user_id"]
                    truncated_assessment_name = (
                        assessment_name[:255] if assessment_name and len(assessment_name) > 255 else assessment_name
                    )
                    truncated_assessment_description = (
                        assessment_description[:255]
                        if assessment_description and len(assessment_description) > 255
                        else assessment_description
                    )

                    if assessment_id:
                        # New format with assessment_id
                        cur.execute(
                            """
                            INSERT INTO quiz_creation_logs
                            (user_id, assessment_name, assessment_description, total_questions, assessment_id)
                            VALUES (%s, %s, %s, %s, %s)
                            ON CONFLICT DO NOTHING
                            """,
                            (
                                user_id,
                                truncated_assessment_name,
                                truncated_assessment_description,
                                entry["total_questions"],
                                assessment_id,
                            ),
                        )
                    else:
                        # Legacy format without assessment_id
                        cur.execute(
                            """
                            INSERT INTO quiz_creation_logs
                            (user_id, assessment_name, assessment_description, total_questions)
                            VALUES (%s, %s, %s, %s)
                            ON CONFLICT DO NOTHING
                            """,
                            (
                                user_id,
                                truncated_assessment_name,
                                truncated_assessment_description,
                                entry["total_questions"],
                            ),
                        )
                conn.commit()
    except psycopg2.DatabaseError as db_error:
        log_database_error("insert", "quiz_creation_logs", db_error)
    except ValueError as val_error:
        error("Value error during quiz creation log insertion", exception=val_error)
    except Exception as e:
        log_database_error("insert", "quiz_creation_logs", e)
